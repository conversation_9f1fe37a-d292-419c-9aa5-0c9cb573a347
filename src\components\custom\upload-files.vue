<script lang="tsx" setup>
import { onMounted, ref, watch, computed } from 'vue';
import { NButton, NDataTable, NInput } from 'naive-ui';
import type { UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui';
import type { TableColumn } from 'naive-ui/es/data-table/src/interface';
import { useDraggable } from 'vue-draggable-plus';
import { uploadApi } from '@/service/api';
import { type CompressorOptions, useCompressor } from '@/hooks/business/compressor';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'UploadFiles',
  inheritAttrs: false
});

interface Props {
  options?: CompressorOptions;
}

const props = defineProps<Props>();

const value = defineModel<Api.Common.UploadFile[]>('value', {
  default: () => []
});

const fileList = computed<UploadFileInfo[]>(() => {
  return value.value.map(file => ({
    id: file.id.toString(),
    name: file.name,
    url: file.url.startsWith('http') ? file.url : import.meta.env.VITE_IMAGE_BASE_URL + file.url,
    status: 'finished'
  }));
});

async function handleBeforeUpload({ file }: { file: UploadFileInfo }) {
  try {
    file.file = await useCompressor(file.file as File, props.options);
  } catch {}
}

async function handleUpload({ file, onFinish, onError, onProgress }: UploadCustomRequestOptions) {
  const formData = new FormData();
  formData.append('file', file.file as File);

  const { data, error } = await uploadApi.file(formData, onProgress);
  if (!error) {
    value.value.push({
      id: file.id.toString(),
      name: file.name,
      size: file.file?.size || 0,
      type: file.file?.type || '',
      url: data.fileName
    });
    window.$message?.success('上传成功');
    onFinish();
  } else {
    window.$message?.error('上传失败');
    onError();
  }
}

async function handleRemove(e: any) {
  if (e.file.status !== 'finished') {
    // 对于未完成的文件，直接从value中移除
    value.value = value.value.filter((_, index) => index !== e.index);
    return;
  }

  const { error } = await uploadApi.remove(value.value[e.index].url);
  if (!error) {
    value.value = value.value.filter((_, index) => index !== e.index);
    window.$message?.success('删除成功');
  } else {
    window.$message?.error('删除失败');
  }
}

const mode = ref<'view' | 'edit'>('view');

// 表格引用
const tableRef = ref<any>(null);

const columns: TableColumn<Api.Common.UploadFile>[] = [
  {
    key: 'drag',
    title: '',
    align: 'right',
    width: 48,
    render: () => <SvgIcon icon="ph:dots-six-vertical" class="inline text-icon" />
  },
  {
    key: 'name',
    title: '名称',
    align: 'left',
    render: (row: Api.Common.UploadFile) => (
      <NInput
        value={row.name}
        onUpdateValue={(val: string) => {
          row.name = val;
          // 手动触发fileList更新
          const index = fileList.value.findIndex(file => file.id === row.id.toString());
          if (index !== -1) {
            fileList.value[index].name = val;
          }
        }}
        placeholder="请输入文件名"
      />
    )
  },
  {
    key: 'size',
    title: '大小',
    align: 'center',
    render: (row: Api.Common.UploadFile) => {
      const sizeInKB = Math.round(row.size / 1024);
      const sizeInMB = (row.size / (1024 * 1024)).toFixed(2);
      return sizeInKB > 1024 ? `${sizeInMB} MB` : `${sizeInKB} KB`;
    }
  }
];

// 初始化拖拽
const initDraggable = () => {
  const tbody = tableRef.value?.$el?.querySelector('.n-data-table-tbody');
  if (tbody && value.value.length > 0) {
    useDraggable(tbody, value, {
      handle: '.n-data-table-td[data-col-key="drag"]',
      animation: 150
    });
  }
};

onMounted(() => {
  // 等待表格渲染完成
  setTimeout(initDraggable, 300);
});

// 监听数据变化，当有数据时重新初始化拖拽
watch(
  () => value.value.length,
  newLength => {
    if (newLength > 0 && mode.value === 'edit') {
      // 等待 DOM 更新后初始化拖拽
      setTimeout(initDraggable, 100);
    }
  }
);

// 监听模式变化，切换到编辑模式时初始化拖拽
watch(mode, newMode => {
  if (newMode === 'edit' && value.value.length > 0) {
    // 等待表格渲染完成
    setTimeout(initDraggable, 300);
  }
});
</script>

<template>
  <div class="upload-files flex flex-col gap-12px">
    <NUpload
      v-if="mode === 'view'"
      :key="value.length + JSON.stringify(value.map(f => f.id))"
      multiple
      directory-dnd
      :file-list="fileList"
      :max="5"
      list-type="image"
      show-download-button
      :custom-request="handleUpload"
      v-bind="$attrs"
      @before-upload="handleBeforeUpload"
      @remove="handleRemove"
    >
      <NUploadDragger>
        <div class="mb-12px">
          <icon-ph-upload class="text-40px" />
        </div>
        <NText class="text-base">点击或者拖动文件到该区域来上传</NText>
        <NP class="mt-2" depth="3">请不要上传敏感数据，比如你的银行卡号和密码，信用卡号有效期和安全码</NP>
      </NUploadDragger>
    </NUpload>
    <NDataTable
      v-show="mode === 'edit'"
      ref="tableRef"
      :columns="columns"
      :data="value"
      size="small"
      striped
      :bordered="false"
      :row-key="row => row.id"
      class="b-t-1px sm:h-full b-auto"
    />
    <div class="flex justify-center">
      <NButton
        v-if="fileList.length > 0"
        secondary
        strong
        :focusable="false"
        @click="mode = mode === 'view' ? 'edit' : 'view'"
      >
        {{ mode === 'view' ? '进入编辑模式' : '返回上传模式' }}
      </NButton>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.n-upload) {
  .n-upload-file-list {
    .n-upload-file.n-upload-file--image-type {
      .n-upload-file-info {
        .n-upload-file-info__thumbnail {
          font-size: 0px;
          color: transparent;
          overflow: hidden;
        }
      }
    }
  }
}
</style>
